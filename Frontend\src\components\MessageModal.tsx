import React from 'react';
import MessageThread from './MessageThread';

interface MessageModalProps {
  isOpen: boolean;
  onClose: () => void;
  reportId: number;
  reporterAddress: string;
  agentAddress?: string;
  isAgent?: boolean;
}

const MessageModal: React.FC<MessageModalProps> = ({
  isOpen,
  onClose,
  reportId,
  reporterAddress,
  agentAddress,
  isAgent = false
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm">
      <div className="w-full max-w-2xl max-h-[80vh] overflow-hidden">
        <MessageThread
          reportId={reportId}
          reporterAddress={reporterAddress}
          agentAddress={agentAddress}
          onClose={onClose}
          isAgent={isAgent}
        />
      </div>
    </div>
  );
};

export default MessageModal;
